"use client";

import React, { useState, useEffect } from "react";
import {
  Loader2,
  <PERSON>fresh<PERSON><PERSON>,
  AlertCircle,
  TrendingUp,
  Settings,
} from "lucide-react";
import Link from "next/link";
import { PaginatedCoinSelector } from "@/components/paginated-coin-selector";
import { AnalysisResult } from "@/components/analysis-result";
import { KlineChart } from "@/components/kline-chart";
import { TradingAdvice } from "@/components/trading-advice";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { configService } from "@/lib/config-service";
import { tradingConfigService } from "@/lib/trading-config-service";

export default function TradingPage() {
  const [selectedCoin, setSelectedCoin] = useState<string>("BTCUSDT");
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [marketData, setMarketData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [riskTolerance, setRiskTolerance] = useState<"LOW" | "MEDIUM" | "HIGH">(
    "MEDIUM"
  );
  const [lastAnalysisTime, setLastAnalysisTime] = useState<Date | null>(null);
  const [isConfigured, setIsConfigured] = useState(false);
  const [isTradingConfigured, setIsTradingConfigured] = useState(false);

  useEffect(() => {
    if (selectedCoin) {
      fetchMarketData();
    }
  }, [selectedCoin]);

  useEffect(() => {
    // 检查AI配置状态
    const config = configService.getConfig();
    setIsConfigured(!!config.apiKey);

    // 检查交易API配置状态
    const tradingConfigured = tradingConfigService.isConfigured();
    setIsTradingConfigured(tradingConfigured);
  }, []);

  const fetchMarketData = async () => {
    const loadingToast = toast.loading("正在获取市场数据...", {
      description: `正在获取 ${selectedCoin} 的实时数据`,
    });

    try {
      setError(null);
      const response = await fetch(`/api/klines?symbol=${selectedCoin}`);
      const result = await response.json();

      if (result.success) {
        setMarketData(result.data);
        toast.dismiss(loadingToast);
        toast.success("✅ 市场数据获取成功！", {
          description: `${selectedCoin} 的多时间维度数据已更新`,
          duration: 3000,
        });
      } else {
        const errorMsg = result.error || "获取市场数据失败";
        setError(errorMsg);
        toast.dismiss(loadingToast);
        toast.error("❌ 获取市场数据失败", {
          description: errorMsg,
          duration: 4000,
        });
      }
    } catch (error) {
      console.error("获取市场数据失败:", error);
      const errorMsg = "获取市场数据失败，请检查网络连接";
      setError(errorMsg);
      toast.dismiss(loadingToast);
      toast.error("❌ 网络连接失败", {
        description: errorMsg,
        duration: 4000,
      });
    }
  };

  const performAnalysis = async () => {
    if (!selectedCoin) {
      toast.error("❌ 请先选择币种", {
        description: "请在上方选择要分析的加密货币",
        duration: 3000,
      });
      return;
    }

    if (!isConfigured) {
      toast.error("❌ 配置缺失", {
        description: "请先配置 OpenAI API 设置",
        duration: 4000,
        action: {
          label: "前往设置",
          onClick: () => window.open("/settings", "_blank"),
        },
      });
      return;
    }

    const loadingToast = toast.loading("🤖 AI正在分析中...", {
      description: `正在分析 ${selectedCoin} 的市场数据和技术指标`,
    });

    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          symbol: selectedCoin,
          riskTolerance,
          includeIndicators: true,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAnalysisData(result.data);
        setLastAnalysisTime(new Date());
        toast.dismiss(loadingToast);
        toast.success("🎉 AI分析完成！", {
          description: `${selectedCoin} 的智能分析报告已生成，包含交易建议和风险评估`,
          duration: 4000,
        });
      } else {
        const errorMsg = result.error || "AI分析失败";
        setError(errorMsg);
        toast.dismiss(loadingToast);
        toast.error("❌ AI分析失败", {
          description: errorMsg,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("AI分析失败:", error);
      const errorMsg = "AI分析失败，请稍后重试";
      setError(errorMsg);
      toast.dismiss(loadingToast);
      toast.error("❌ 分析服务异常", {
        description: errorMsg,
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCoinSelect = (symbol: string) => {
    setSelectedCoin(symbol);
    setAnalysisData(null);
    setError(null);
    setLastAnalysisTime(null);

    // 自动获取市场数据
    if (symbol) {
      setTimeout(() => {
        fetchMarketData();
      }, 500); // 延迟500ms让toast显示完成
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
          <TrendingUp className="h-8 w-8 text-blue-500" />
          <span>AI加密货币交易分析</span>
        </h1>
        <p className="text-gray-600">
          基于滚仓策略的智能交易分析，助您把握市场机会
        </p>
      </div>

      {/* 币种选择器 */}
      <PaginatedCoinSelector
        selectedCoin={selectedCoin}
        onCoinSelect={handleCoinSelect}
      />

      {/* 分析控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>分析设置</span>
            {lastAnalysisTime && (
              <Badge variant="outline">
                最后分析: {formatTime(lastAnalysisTime)}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">风险偏好:</span>
              <Select
                value={riskTolerance}
                onValueChange={(value: any) => setRiskTolerance(value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">保守型</SelectItem>
                  <SelectItem value="MEDIUM">平衡型</SelectItem>
                  <SelectItem value="HIGH">激进型</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={performAnalysis}
              disabled={loading || !selectedCoin || !isConfigured}
              className="flex items-center space-x-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              <span>{loading ? "分析中..." : "开始AI分析"}</span>
            </Button>

            {marketData && (
              <Button
                variant="outline"
                onClick={fetchMarketData}
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>刷新数据</span>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 配置提示 */}
      {(!isConfigured || !isTradingConfigured) && (
        <div className="space-y-3">
          {!isConfigured && (
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>AI 分析功能需要配置 OpenAI API 设置才能使用</span>
                <Link href="/settings">
                  <Button size="sm" variant="outline">
                    前往设置
                  </Button>
                </Link>
              </AlertDescription>
            </Alert>
          )}

          {!isTradingConfigured && (
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>实际交易功能需要配置交易所 API 设置才能使用</span>
                <Link href="/settings">
                  <Button size="sm" variant="outline">
                    配置交易API
                  </Button>
                </Link>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 主要内容区域 */}
      {marketData && (
        <Tabs defaultValue="chart" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chart">价格图表</TabsTrigger>
            <TabsTrigger value="analysis" disabled={!analysisData}>
              AI分析结果
            </TabsTrigger>
            <TabsTrigger value="advice" disabled={!analysisData}>
              交易建议
            </TabsTrigger>
          </TabsList>

          <TabsContent value="chart" className="mt-6">
            <KlineChart data={marketData} symbol={selectedCoin} />
          </TabsContent>

          <TabsContent value="analysis" className="mt-6">
            {analysisData ? (
              <AnalysisResult data={analysisData} />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center space-y-3">
                    <TrendingUp className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="text-gray-500">请先进行AI分析</p>
                    <Button onClick={performAnalysis} disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          分析中...
                        </>
                      ) : (
                        "开始分析"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="advice" className="mt-6">
            {analysisData ? (
              <TradingAdvice
                advice={analysisData.advice}
                positionManagement={analysisData.positionManagement}
                rollingStrategy={analysisData.rollingStrategy}
                riskReward={analysisData.riskReward}
                symbol={selectedCoin}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center space-y-3">
                    <TrendingUp className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="text-gray-500">
                      请先进行AI分析以获取交易建议
                    </p>
                    <Button onClick={performAnalysis} disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          分析中...
                        </>
                      ) : (
                        "开始分析"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}

      {/* 加载状态 */}
      {!marketData && !error && selectedCoin && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center space-y-3">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-500">
                正在获取 {selectedCoin} 的市场数据...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 免责声明 */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>免责声明:</strong>
          本工具提供的分析和建议仅供参考，不构成投资建议。
          加密货币交易存在高风险，请根据自身情况谨慎决策，并做好风险管理。
        </AlertDescription>
      </Alert>
    </div>
  );
}
