"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Search,
  TrendingUp,
  TrendingDown,
  ArrowUpDown,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { CoinInfo, PaginatedResponse } from "@/types/trading";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface PaginatedCoinSelectorProps {
  selectedCoin?: string;
  onCoinSelect: (symbol: string) => void;
  className?: string;
}

export function PaginatedCoinSelector({
  selectedCoin,
  onCoinSelect,
  className,
}: PaginatedCoinSelectorProps) {
  const [coins, setCoins] = useState<CoinInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sortBy, setSortBy] = useState<
    "symbol" | "price" | "change24h" | "volume24h"
  >("price");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // 热门币种列表
  const popularSymbols = [
    "BTCUSDT",
    "ETHUSDT",
    "BNBUSDT",
    "ADAUSDT",
    "SOLUSDT",
    "XRPUSDT",
    "DOGEUSDT",
    "AVAXUSDT",
    "DOTUSDT",
    "MATICUSDT",
  ];

  // 防抖搜索
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const fetchCoins = useCallback(async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        paginated: "true",
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
        sortBy,
        sortOrder,
      });

      if (searchTerm.trim()) {
        params.append("search", searchTerm.trim());
      }

      const response = await fetch(`/api/coins?${params}`);
      const result: PaginatedResponse<CoinInfo> = await response.json();

      if (result.success) {
        setCoins(result.data);
        setPagination(result.pagination);

        if (currentPage === 1) {
          toast.success("✅ 币种数据加载成功", {
            description: `共找到 ${result.pagination.total} 个交易对`,
            duration: 2000,
          });
        }
      } else {
        console.error("获取币种失败:", result.error);
        toast.error("❌ 获取币种数据失败", {
          description: result.error || "无法获取币种列表，请稍后重试",
          duration: 4000,
        });
      }
    } catch (error: any) {
      console.error("获取币种失败:", error);
      toast.error("❌ 网络连接失败", {
        description: "无法连接到服务器，请检查网络连接",
        duration: 4000,
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortBy, sortOrder, searchTerm]);

  // 初始加载
  useEffect(() => {
    fetchCoins();
  }, [fetchCoins]);

  // 搜索防抖
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      setCurrentPage(1); // 搜索时重置到第一页
      fetchCoins();
    }, 500);

    setSearchTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [searchTerm]);

  const handleCoinSelect = (symbol: string) => {
    onCoinSelect(symbol);
    toast.success("📈 币种已选择", {
      description: `已选择 ${symbol}，可以开始获取市场数据`,
      duration: 2000,
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSortChange = (newSortBy: string) => {
    if (newSortBy === sortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(newSortBy as "symbol" | "price" | "change24h" | "volume24h");
      setSortOrder("desc");
    }
    setCurrentPage(1);
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(2);
    } else if (price >= 0.01) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  };

  const formatChange = (change: number) => {
    return `${change >= 0 ? "+" : ""}${change.toFixed(2)}%`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000000) {
      return `${(volume / 1000000000).toFixed(1)}B`;
    } else if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toFixed(0);
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field)
      return <ArrowUpDown className="h-3 w-3 text-gray-400" />;
    return sortOrder === "asc" ? (
      <TrendingUp className="h-3 w-3 text-blue-500" />
    ) : (
      <TrendingDown className="h-3 w-3 text-blue-500" />
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Search className="h-5 w-5" />
          <span>选择交易币种</span>
          <Badge variant="secondary" className="ml-auto">
            {pagination.total} 个币种
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 搜索和筛选 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索币种名称或代码..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => {
              setPageSize(parseInt(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 / 页</SelectItem>
              <SelectItem value="20">20 / 页</SelectItem>
              <SelectItem value="50">50 / 页</SelectItem>
              <SelectItem value="100">100 / 页</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 排序按钮 */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={sortBy === "price" ? "default" : "outline"}
            size="sm"
            onClick={() => handleSortChange("price")}
            className="flex items-center space-x-1"
          >
            <span>价格</span>
            {getSortIcon("price")}
          </Button>
          <Button
            variant={sortBy === "volume24h" ? "default" : "outline"}
            size="sm"
            onClick={() => handleSortChange("volume24h")}
            className="flex items-center space-x-1"
          >
            <span>成交量</span>
            {getSortIcon("volume24h")}
          </Button>
          <Button
            variant={sortBy === "change24h" ? "default" : "outline"}
            size="sm"
            onClick={() => handleSortChange("change24h")}
            className="flex items-center space-x-1"
          >
            <span>涨跌幅</span>
            {getSortIcon("change24h")}
          </Button>
          <Button
            variant={sortBy === "symbol" ? "default" : "outline"}
            size="sm"
            onClick={() => handleSortChange("symbol")}
            className="flex items-center space-x-1"
          >
            <span>名称</span>
            {getSortIcon("symbol")}
          </Button>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        )}

        {/* 币种列表 */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {coins.map((coin) => (
              <Button
                key={coin.symbol}
                variant={selectedCoin === coin.symbol ? "default" : "outline"}
                className="h-auto p-3 flex flex-col items-start space-y-1"
                onClick={() => handleCoinSelect(coin.symbol)}
              >
                <div className="flex items-center justify-between w-full">
                  <span className="font-semibold text-sm">{coin.name}</span>
                  {popularSymbols.includes(coin.symbol) && (
                    <Badge variant="secondary" className="text-xs">
                      热门
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between w-full">
                  <span className="text-xs text-gray-500">{coin.symbol}</span>
                  <div className="flex items-center space-x-1">
                    {coin.change24h >= 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                    <span
                      className={`text-xs ${
                        coin.change24h >= 0 ? "text-green-500" : "text-red-500"
                      }`}
                    >
                      {formatChange(coin.change24h)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between w-full">
                  <span className="text-sm font-medium">
                    ${formatPrice(coin.price)}
                  </span>
                  <span className="text-xs text-gray-400">
                    Vol: {formatVolume(coin.volume24h)}
                  </span>
                </div>
              </Button>
            ))}
          </div>
        )}

        {/* 分页控件 */}
        {!loading && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              第 {pagination.page} 页，共 {pagination.totalPages} 页
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() =>
                      pagination.hasPrev &&
                      handlePageChange(pagination.page - 1)
                    }
                    className={
                      !pagination.hasPrev
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>

                {/* 页码显示逻辑 */}
                {Array.from(
                  { length: Math.min(5, pagination.totalPages) },
                  (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => handlePageChange(pageNum)}
                          isActive={pageNum === pagination.page}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  }
                )}

                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      pagination.hasNext &&
                      handlePageChange(pagination.page + 1)
                    }
                    className={
                      !pagination.hasNext
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}

        {/* 无数据提示 */}
        {!loading && coins.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>没有找到匹配的币种</p>
            <p className="text-sm">请尝试其他搜索关键词</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
